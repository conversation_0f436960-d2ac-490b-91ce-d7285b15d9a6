# 🎯 Demo: How to Use `useAutoMeetingEnd` Hook in VideoConference.js

## 📋 Overview
This demo shows how the `useAutoMeetingEnd` hook is integrated into VideoConference.js to provide SaaS auto meeting extension functionality.

## 🔧 Integration Steps

### 1. Import the Hook
```javascript
import { useAutoMeetingEnd } from "../SaaS/fetaures/AutoMettingExtend/AutoMeetingExtend";
```

### 2. Use the Hook in Component
```javascript
// SaaS Auto Meeting End Hook
const {
  autoMeetingEndTime,
  endMeetingNotification,
  setEndMeetingNotification,
  extendMeeting,
  handleExtendMeetingReceived,
  isSaaSEnabled,
  shouldWork
} = useAutoMeetingEnd({
  meetingDetails: props.meetingDetails,
  isHost: props.isHost,
  room,
  coHostToken,
  setIsMeetingFinished: props.setIsMeetingFinished,
  isCoHost,
  setToastNotification,
  setToastStatus,
  setShowToast
});
```

### 3. Handle Data Received Events
```javascript
// In the onDataReceived function
} else if (
  data.action &&
  data.action === DataReceivedEvent.EXTEND_MEETING_END_TIME
) {
  // Handle extend meeting received from SaaS
  if (shouldWork) {
    handleExtendMeetingReceived();
  }
}
```

### 4. Render the Notification Modal
```javascript
{/* SaaS Auto Meeting End Notification */}
{endMeetingNotification && shouldWork && (
  <div className="extend-meeting-modal">
    <div className="modal-content">
      <h3>Meeting Ending Soon</h3>
      <p>This meeting will end in 10 minutes.</p>
      <div className="modal-actions">
        <Button 
          type="primary" 
          onClick={extendMeeting}
          style={{ marginRight: '10px' }}
        >
          Extend Meeting (10 min)
        </Button>
        <Button 
          onClick={() => setEndMeetingNotification(false)}
        >
          Dismiss
        </Button>
      </div>
    </div>
  </div>
)}
```

## ✅ What This Integration Provides

### 🎯 **SaaS-Only Functionality**
- ✅ Only works when `isSaaS` is `true` in SaasConfigContext
- ✅ Only works when `auto_meeting_end` is enabled in meeting config
- ✅ Safe fallbacks when SaaS is disabled

### 🔔 **Auto Meeting End Notifications**
- ✅ Shows notification 10 minutes before meeting ends
- ✅ Different behavior for hosts/co-hosts vs participants
- ✅ Toast notifications for regular participants
- ✅ Modal for hosts/co-hosts with extend option

### ⏰ **Meeting Extension**
- ✅ Hosts/co-hosts can extend meeting by 10 minutes
- ✅ Broadcasts extension to all participants
- ✅ Updates meeting end time automatically
- ✅ Shows success/error notifications

### 🔄 **Data Synchronization**
- ✅ Handles `EXTEND_MEETING_END_TIME` data events
- ✅ Syncs meeting extension across all participants
- ✅ Updates local meeting end time when extension is received

## 🎨 **UI Components**

### Modal Styling (VideoConference.scss)
```scss
.extend-meeting-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;

  .modal-content {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    max-width: 400px;
    width: 90%;
  }
}
```

## 🔍 **Hook Return Values**

| Property | Type | Description |
|----------|------|-------------|
| `autoMeetingEndTime` | Moment | Current meeting end time |
| `endMeetingNotification` | Boolean | Whether to show notification modal |
| `setEndMeetingNotification` | Function | Control notification visibility |
| `extendMeeting` | Function | Extend meeting by 10 minutes |
| `handleExtendMeetingReceived` | Function | Handle extension from other participants |
| `isSaaSEnabled` | Boolean | Whether SaaS is enabled |
| `shouldWork` | Boolean | Whether hook should be active |

## 🚀 **Usage Example**

```javascript
// The hook automatically:
// 1. Checks if SaaS is enabled
// 2. Monitors meeting end time
// 3. Shows notifications at appropriate times
// 4. Handles meeting extensions
// 5. Syncs data across participants

// You just need to:
// 1. Import and use the hook
// 2. Render the notification modal
// 3. Handle the data received event
```

## ⚡ **Performance Optimized**
- ✅ Minimal dependencies in useEffect
- ✅ Only runs when SaaS is enabled
- ✅ Efficient interval management
- ✅ Proper cleanup on unmount

## 🎯 **Ready to Use!**
The integration is complete and ready for production use in ControlBar.js or any other component that needs SaaS auto meeting end functionality.
