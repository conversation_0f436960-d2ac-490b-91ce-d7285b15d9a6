.features-cards {
    position: relative; 
    display: flex;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
    width: 500px;
    transition: 0.3s;
    overflow: hidden; 

    @media screen and (max-width: 1463px) {
        width: 400px;
    }

    @media screen and (max-width: 1183px) {
        width: 300px;
    }

    @media screen and (max-width: 896px) {
        width: 450px;
    }

    @media screen and (max-width: 586px) {
        width: 100%;
        
    }

    &:hover {
        box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
    }
    img {
        width: 500px;
        height: 200px;
        object-fit: cover; 
        border-radius: 12px; 
        transition: 0.3s;
        filter: blur(0.5px);
    }

    div {
        position: absolute;
        top: 20px; 
        left: 20px; 
        color: white; 
        z-index: 1; 

        span {
            font-size: 1.2rem;
            color: white;
            font-weight: 700;
            margin-bottom: 10px;
        }

        p {
            font-size: 0.9rem;
            color: white; 
            font-weight: 400;
            margin-bottom: 10px;
            padding-left: 3rem !important;
            padding-right: 3rem !important;
        }
        
       
           
        
    }
}

.solutions-card {
    position: relative; 
    display: flex;
    flex-direction: column-reverse;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
    padding: 10px;
    transition: 0.3s;
    overflow: hidden; 

    &:hover {
        box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
    }
    
    span {
        position: absolute;
        top: 20px;  
        left: 20px; 
        font-size: 1.2rem;
        color: #25E89F;
        font-weight: 700;
        margin-bottom: 10px;
        margin: 0;
        z-index: 1;

        @media screen and (max-width: 586px) {
            font-size: 1rem;
        }
    }
}
