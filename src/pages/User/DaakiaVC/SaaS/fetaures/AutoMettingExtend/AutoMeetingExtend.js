import { useState, useRef, useEffect } from "react";
import moment from "moment";
import { SaasService } from "../../services/saasServices"; // Fixed path
import { DataReceivedEvent, constants } from "../../../utils/constants";
import { setLocalStorage } from "../../../utils/helper";
import { useSaasConfigContext } from "../../../context/SaasConfigContext";

export const useAutoMeetingEnd = ({
  meetingDetails,
  isHost,
  room,
  coHostToken,
  setIsMeetingFinished,
  isCoHost,
  setToastNotification,
  setToastStatus,
  setShowToast
}) => {
  // Get SaaS context
  const { isSaaS, saasHostToken } = useSaasConfigContext();

  // Only work when SaaS is enabled
  const shouldWork = isSaaS && meetingDetails?.meeting_config?.auto_meeting_end === 1;

  const [autoMeetingEndTime, setAutoMeetingEndTime] = useState(
    moment(meetingDetails?.meeting_config?.auto_meeting_end_schedule)
  );
  const [endMeetingNotification, setEndMeetingNotification] = useState(false);

  const dismissed = useRef(false);
  const hasShownToast = useRef(false);

  const extendMeeting = async () => {
    try {
      const response = await SaasService.extendMeeting(
        true,
        meetingDetails?.room_uid,
        saasHostToken || coHostToken 
      );

      if (response?.success === 1) {
        setEndMeetingNotification(false);
        // add 10 min to meetingEndTime
        let meetingEndTime = autoMeetingEndTime;
        meetingEndTime = meetingEndTime.add(10, "minutes");
        meetingDetails.meeting_config.auto_meeting_end_schedule = meetingEndTime;
        setAutoMeetingEndTime(meetingEndTime);
        dismissed.current = false;
        // broadcast the event in data channel
        const encoder = new TextEncoder();
        const data = encoder.encode(
          JSON.stringify({
            action: DataReceivedEvent.EXTEND_MEETING_END_TIME,
          })
        );
        room.localParticipant.publishData(data, { reliable: true });
        setToastNotification("Meeting extended successfully!");
        setToastStatus("success");
        setShowToast(true);
      } else {
        setToastNotification("Error extending meeting!");
        setToastStatus("error");
        setShowToast(true);
        return;
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  useEffect(() => {
    if (!room || !shouldWork) return;

    const intervalId = setInterval(() => {
      const currentDateTime = moment();
      const timeDifference = autoMeetingEndTime.diff(currentDateTime, "seconds");
      // If meeting has officially ended
      if (timeDifference <= 0) {
        setIsMeetingFinished(() => true);
        setLocalStorage(constants.CO_HOST_TOKEN, null);
        room.disconnect();
      } else if (timeDifference <= 600) {
        if(isHost || isCoHost){
          if(!dismissed.current){
            setEndMeetingNotification(true);
          }
        } else {
          if(!hasShownToast.current){
            setToastNotification(`Meeting will end in 10 minutes`);
            setToastStatus("warning");
            setShowToast(true);
            hasShownToast.current = true;
          }
        }
      }
    }, 10000); // Run every 10 sec

    return () => clearInterval(intervalId); // Cleanup interval on unmount
  }, [room, autoMeetingEndTime, shouldWork, setIsMeetingFinished, isHost, isCoHost, setToastNotification, setToastStatus, setShowToast]);

  // Function to handle data received (call this from VideoConference.js)
  const handleExtendMeetingReceived = () => {
    setAutoMeetingEndTime((prev) => prev.add(10, "minutes"));
    setToastNotification("Meeting extended by 10 minutes");
    setToastStatus("info");
    setShowToast(true);
  };

  // Return everything you need (with SaaS check)
  return {
    autoMeetingEndTime,
    setAutoMeetingEndTime,
    endMeetingNotification: shouldWork ? endMeetingNotification : false,
    setEndMeetingNotification,
    dismissed,
    hasShownToast,
    extendMeeting: shouldWork ? extendMeeting : () => {},
    handleExtendMeetingReceived: shouldWork ? handleExtendMeetingReceived : () => {},
    isSaaSEnabled: isSaaS,
    shouldWork
  };
};
