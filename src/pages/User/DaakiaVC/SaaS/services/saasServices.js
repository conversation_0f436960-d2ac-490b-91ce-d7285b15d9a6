import { datadogLogs } from "@datadog/browser-logs";
import { APIrequest } from "../../API/axios";
import { Endpoints } from "../routes/saasRoutes";
export const SaasService = {
  extendMeeting: async (extended, id, token = null) => {
    try {
      const response = await APIrequest({
        method: Endpoints.extend_meeting.method,
        endpoint: Endpoints.extend_meeting.url,
        payload: {
          is_extend_time: extended,
          meeting_uid: id,
        },
        token,
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error extending meeting", {
          response,
          payload: {
            is_extend_time: extended,
            meeting_uid: id,
          },
          endpoint: Endpoints.extend_meeting.url,
          // user,
        });
      } else {
        datadogLogs.logger.info("Success extending meeting", {
          response,
          payload: {
            is_extend_time: extended,
            meeting_uid: id,
          },
          endpoint: Endpoints.extend_meeting.url,
          // user,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
};
