import { datadogLogs } from "@datadog/browser-logs";
import { APIrequest } from "../API/axios";
import { Endpoints } from "../API/Endpoints/routes";
// import { apiLogger } from "../utils/helper";

export const PrejoinService = {
  getMeetingDetails: async (id, token = null) => {
    try {
      const meetingUrl = Endpoints.get_meeting_details(id);
      const response = await APIrequest({
        method: meetingUrl.method,
        endpoint: meetingUrl.url,
        needLogHeader:true,
        token,
      });
      if (response.success === 0) {
        datadogLogs.logger.error("Error getting meeting details", {
          response,
          endpoint: meetingUrl.url,
        });
      } else {
        datadogLogs.logger.info("Success Getting meeting details", {
          response,
          endpoint: meetingUrl.url,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  addParticipantToLobby: async (meetindId, participantName) => {
    try {
      const response = await APIrequest({
        method: Endpoints.add_participant_to_lobby.method,
        endpoint: Endpoints.add_participant_to_lobby.url,
        payload: {
          meeting_uid: meetindId,
          display_name: participant<PERSON>ame,
        },
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error adding participant to lobby", {
          response,
          payload: {
            meeting_uid: meetindId,
            display_name: participantName,
          },
          endpoint: Endpoints.add_participant_to_lobby.url,
        });
      } else {
        datadogLogs.logger.info("Success adding participant to lobby", {
          response,
          payload: {
            meeting_uid: meetindId,
            display_name: participantName,
          },
          endpoint: Endpoints.add_participant_to_lobby.url,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  joinMeeting: async (payload, user, token=null) => {
    try {
      const response = await APIrequest({
        method: Endpoints.join_meeting.method,
        endpoint: Endpoints.join_meeting.url,
        payload,
        token,
        needLogHeader:true,
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error joining meeting", {
          response,
          payload,
          endpoint: Endpoints.join_meeting.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success joining meeting", {
          response,
          payload,
          endpoint: Endpoints.join_meeting.url,
          user,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  verifyPassword: async (email, password, meetingId) => {
    try {
      const response = await APIrequest({
        method: Endpoints.verify_password.method,
        endpoint: Endpoints.verify_password.url,
        payload: {
          email,
          password,
          meeting_uid: meetingId,
        },
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error verifying password", {
          response,
          payload: {
            email,
            password,
            meeting_uid: meetingId,
          },
          endpoint: Endpoints.verify_password.url,
        });
      } else {
        datadogLogs.logger.info("Success verifying password", {
          response,
          payload: {
            email,
            password,
            meeting_uid: meetingId,
          },
          endpoint: Endpoints.verify_password.url,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  getMeetingFeatures: async (subscriptionId) => {
    try {
      const featuresLink = Endpoints.get_meeting_features(subscriptionId);
      const response = await APIrequest({
        method: featuresLink.method,
        endpoint: featuresLink.url,
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error getting meeting features", {
          response,
          endpoint: featuresLink.url,
        });
      } else {
        datadogLogs.logger.info("Success getting meeting features", {
          response,
          endpoint: featuresLink.url,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  commonPasswordVerify: async (meetingId, password) => {
    try {
      const response = await APIrequest({
        method: Endpoints.common_password_verify.method,
        endpoint: Endpoints.common_password_verify.url,
        payload: {
          password,
          meeting_uid: meetingId,
        },
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error verifying common password", {
          response,
          payload: {
            password,
            meeting_uid: meetingId,
          },
          endpoint: Endpoints.common_password_verify.url,
        });
      } else {
        datadogLogs.logger.info("Success verifying common password", {
          response,
          payload: {
            password,
            meeting_uid: meetingId,
          },
          endpoint: Endpoints.common_password_verify.url,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
};
