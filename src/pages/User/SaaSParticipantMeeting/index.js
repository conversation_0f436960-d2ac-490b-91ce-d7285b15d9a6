/* eslint-disable */
import React, { useEffect, useState, useRef, useCallback } from "react";
import { useParams } from "react-router-dom";
import { datadogLogs } from "@datadog/browser-logs";
import { Spin, message } from "antd";
import DaakiaVC from "../DaakiaVC/index.page";
import { DATA_DOG_CONFIG } from "../../../utils/constants";
import Lobby from "./lobby";
import { SaaSMeetingService } from "../../../services/User/SaaSMeeting/index.service";

// Constants for better maintainability
const REDIRECT_COUNTDOWN_SECONDS = 10;
const DEFAULT_REDIRECT_URL = "https://www.daakia.co.in/";
const FAILURE_RESPONSE_CODE = 0;

/**
 * ParticipantMeetingInvite Component
 * 
 * This component handles the participant meeting invitation flow including:
 * - Meeting validation and configuration fetching
 * - Lobby management
 * - Video conference initialization
 * 
 * @returns {JSX.Element} The rendered component
 */
function ParticipantMeetingInvite() {
  // URL Parameters
  const { meetingId } = useParams();
  
  // Refs
  const dataDogInitialized = useRef(false);
  const lastApiCallTime = useRef(0);
  const COOLDOWN_PERIOD = 10000; // 10 seconds
  
  // Loading and UI States
  const [loading, setLoading] = useState(true);
  const [countdown, setCountdown] = useState(REDIRECT_COUNTDOWN_SECONDS);
  
  // Meeting States
  const [isMeetingExist, setIsMeetingExist] = useState(null);
  const [meetingConfig, setMeetingConfig] = useState(null);
  const [meetingFeatures, setMeetingFeatures] = useState(null);
  const [hostJoined, setHostJoined] = useState(false);
  const [inLobby, setInLobby] = useState(true);
  const [meetingTime, setMeetingTime] = useState(null);

  /**
   * Initialize DataDog logging configuration
   */
  useEffect(() => {
    if (dataDogInitialized.current) return;
    
    datadogLogs.init({
      clientToken: DATA_DOG_CONFIG.TOKEN,
      site: DATA_DOG_CONFIG.SITE,
      service: DATA_DOG_CONFIG.SERVICE,
      env: DATA_DOG_CONFIG.ENV,
      forwardErrorsToLogs: false,
      sessionSampleRate: 100,
    });
    
    dataDogInitialized.current = true;
  }, []);

  /**
   * Log errors to DataDog with consistent format
   * @param {string} operation - The operation that failed
   * @param {Error} error - The error object
   * @param {any} response - The API response (if any)
   */
  const logError = useCallback((operation, error, response = null) => {
    datadogLogs.logger.error(`ERROR: ${operation}`, {
      error: error,
      meeting_Uid: meetingId,
      response: response,
    });
  }, [meetingId]);

  /**
   * Check if the meeting exists and get basic meeting info
   */
  const fetchMeetingExist = useCallback(async () => {
    let response = null;
    try {
      response = await SaaSMeetingService.checkMeetingExistService(meetingId);
      if (response?.success === FAILURE_RESPONSE_CODE) {
        setIsMeetingExist(false);
      } else {
        setIsMeetingExist(true);
        setInLobby(!response?.data?.inviti_can_start_meeting);
        setMeetingTime(response?.data?.meeting_time);
      }
    } catch (error) {
      logError("Check Meeting Exist", error, response);
    }
  }, [meetingId, logError]);

  /**
   * Fetch meeting configuration
   */
  const getMeetingConfig = useCallback(async () => {
    let response = null;
    try {
      response = await SaaSMeetingService.getMeetingConfig(meetingId);
      if (response?.success === FAILURE_RESPONSE_CODE) {
        setMeetingConfig(null);
      } else {
        setMeetingConfig(response?.data);
      }
    } catch (error) {
      logError("Get Meeting Config", error, response);
    }
  }, [meetingId, logError]);

  /**
   * Fetch meeting features and configurations
   */
  const getMeetingFeatures = useCallback(async () => {
    let response = null;
    try {
      response = await SaaSMeetingService.getMeetingFeatures(meetingId);
      if (response?.success === FAILURE_RESPONSE_CODE) {
        setMeetingFeatures(null);
      } else {
        setMeetingFeatures(response?.data?.features);
      }
    } catch (error) {
      logError("Get Meeting Features", error, response);
    }
  }, [meetingId, logError]);

  const checkHostJoinedMeeting = useCallback(async () => {
    setLoading(true);
    let response = null;
    try {
      response = await SaaSMeetingService.checkHostJoinedMeeting(meetingId);
      if (response?.success === FAILURE_RESPONSE_CODE) {
        setHostJoined(false);
        message.error("Host has not joined the meeting yet");
      } else {
        setHostJoined(response?.data?.host_joined);
      }
    } catch (error) {
      logError("Check Host Joined Meeting", error, response);
    } finally {
      setLoading(false);
    }
  }, [meetingId, logError]);

  const handleRefreshWithCooldown = useCallback(async () => {
    const currentTime = Date.now();
    const timeSinceLastCall = currentTime - lastApiCallTime.current;
    if (timeSinceLastCall >= COOLDOWN_PERIOD) {
      lastApiCallTime.current = currentTime;
      await checkHostJoinedMeeting();
    } else {
      const remainingTime = Math.ceil((COOLDOWN_PERIOD - timeSinceLastCall) / 1000);
      message.info(`Please wait ${remainingTime} seconds before making another request`);
    }
  }, [checkHostJoinedMeeting]);

  /**
   * Initialize meeting data on component mount
   * Keeping the original sequential logic as requested
   */
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);

      await fetchMeetingExist();
      
      await checkHostJoinedMeeting();
      await getMeetingConfig();
      await getMeetingFeatures();

      setLoading(false);
    };

    fetchData();
  }, []);

  /**
   * Handle countdown and redirect when meeting doesn't exist
   */
  useEffect(() => {
    if (isMeetingExist === false) {
      setCountdown(REDIRECT_COUNTDOWN_SECONDS); // Reset countdown when this effect runs
      const countdownInterval = setInterval(() => {
        setCountdown((prevCountdown) => {
          if (prevCountdown <= 1) {
            clearInterval(countdownInterval);
            return 0;
          }
          return prevCountdown - 1;
        });
      }, 1000);

      const redirectUrl =
        meetingConfig?.meeting_end_redirect_url || DEFAULT_REDIRECT_URL;
      const redirectTimeout = setTimeout(() => {
        window.location.href = redirectUrl;
      }, REDIRECT_COUNTDOWN_SECONDS * 1000);

      return () => {
        clearInterval(countdownInterval);
        clearTimeout(redirectTimeout);
      };
    }
  }, [isMeetingExist, meetingConfig?.meeting_end_redirect_url]);



  /**
   * Render loading state
   */
  const renderLoading = () => (
    <Spin tip="Loading..." spinning={loading} />
  );

  /**
   * Render error state when meeting doesn't exist
   */
  const renderErrorState = () => (
    <div style={{ display: "block", marginTop: "10%" }}>
      <h4 style={{ textAlign: "center", fontWeight: 600, top: "50%" }}>
        The meeting you are trying <br /> to join does not exist or expired.
        Please contact the host
      </h4>
      <p style={{ textAlign: "center" }}>
        You Will Be Redirected In {countdown} Seconds
      </p>
    </div>
  );


  /**
   * Render lobby component
   */
  const renderLobby = () => (
    <Lobby meetingTime={meetingTime} onRefresh={handleRefreshWithCooldown} />
  );

  /**
   * Render video conference component
   */
  const renderVideoConference = () => (
    <DaakiaVC
      saasConfig={{
        isSaaS: true,
        meetingId: meetingId,
        meetingConfig: meetingConfig,
        meetingFeatures: meetingFeatures,
      }}
    />
  );

  /**
   * Determine which component to render based on current state
   */
  const renderContent = () => {
    // Show loading while initializing
    if (loading || isMeetingExist === null) {
      return renderLoading();
    }

    // Show error if meeting doesn't exist
    if (!isMeetingExist) {
      return renderErrorState();
    }

    // Show lobby if not ready to start
    if (inLobby || !hostJoined) {
      return renderLobby();
    }

    // Show video conference
    return renderVideoConference();
  };

  return (
    <div
      style={{
        height: "100vh",
        width: "100vw",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      {renderContent()}
    </div>
  );
}

export default ParticipantMeetingInvite;
